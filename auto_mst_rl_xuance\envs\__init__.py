"""
自适应MST阈值强化学习环境模块
"""

from auto_mst_rl_xuance.envs.base.mst_env import MSTEnv
from auto_mst_rl_xuance.envs.mappo.ce_env import CEEnv
from auto_mst_rl_xuance.envs.mappo.fp_env import FPEnv
from xuance.environment.multi_agent_env import REGISTRY_MULTI_AGENT_ENV

# 环境注册表
REGISTRY_CUSTOM_ENV = {
    "CEEnv": CEEnv,
    "FPEnv": FPEnv,
}


def register_envs():
    """
    注册自定义环境到XuanCe框架
    """
    # 将我们的环境添加到REGISTRY_MULTI_AGENT_ENV字典中
    for env_id, env_cls in REGISTRY_CUSTOM_ENV.items():
        REGISTRY_MULTI_AGENT_ENV[env_id] = env_cls

    print(f"已注册环境：{list(REGISTRY_CUSTOM_ENV.keys())}")


__all__ = [
    "MSTEnv",
    "CEEnv",
    "FPEnv",
    "REGISTRY_CUSTOM_ENV",
    "register_envs",
]
