# Chase-Escape场景的MAPPO算法配置
# 基础设置
dl_toolbox: "torch"  # 深度学习框架: "torch", "mindspore", "tensorlayer"
project_name: "mst"
logger: "tensorbord"  # 日志工具: "tensorboard", "wandb"
wandb_user_name: "auto_mst_rl"
test_mode: false
device: "cuda:0"  # 计算设备: "cpu", "cuda:0"
distributed_training: false  # 是否使用分布式训练
master_port: "12355"  # 分布式训练主端口
clip_type: 1  # 梯度裁剪类型

# 环境设置
agent: "MAPPO"
env_name: "CEEnv"  # 环境名称
env_id: "CEEnv"  # 环境ID
env_seed: 42  # 环境随机种子
continuous_action: true  # 连续动作空间
render: false  # 是否开启渲染
render_mode: null
fps: 30  # 渲染帧率
vectorize: "SubprocVecMultiAgentEnv"  # 向量化环境类型
running_steps: 2000000  # 总运行步数
eval_interval: 5000  # 评估间隔
test_episode: 20  # 测试轮数

# 环境特定参数
max_id: 50  # 智能体数量
max_episode_steps: 1200  # 最大步数号
mst_min: 0.0  # MST阈值的最小值
mst_max: 20  # MST阈值的最大值
k_neighbors: 7  # 观察中考虑的邻居数量
normalize_obs: true  # 归一化观察
normalize_state: true  # 归一化状态

# 算法设置
algo_name: "MAPPO"
learner: "MAPPO_Clip_Learner"
policy: "Gaussian_MAAC_Policy"  # 连续动作的高斯策略
representation: "Basic_MLP"

# 网络架构设置
representation_args:  # 表示网络参数
  hidden_sizes: [256, 128]
  activation: "relu"
representation_hidden_size: [256, 128]  # 表示网络隐藏层大小（与XuanCe库兼容）
actor_hidden_size: [128, 128]  # Actor网络隐藏层大小
critic_hidden_size: [128, 128]  # Critic网络隐藏层大小
activation: "relu"  # 激活函数
activation_action: "tanh"  # 动作输出层的激活函数，改为sigmoid更平滑
use_parameter_sharing: true  # 是否使用参数共享
use_actions_mask: false  # 是否使用动作掩码

# RNN设置（当使用RNN时）
use_rnn: false  # 是否使用循环神经网络
rnn: "GRU"  # 循环层类型: "GRU", "LSTM"
N_recurrent_layers: 1  # 循环层数量
recurrent_hidden_size: 64  # 循环层隐藏单元数量
dropout: 0  # 丢弃率
normalize: "LayerNorm"  # 层归一化
initialize: "orthogonal"  # 修改为xavier_uniform网络初始化方式
gain: 0.1  # 初始化增益改为更大的值

# 训练参数
seed: 1  # 随机种子
parallels: 32  # 并行环境数量（与稳定版配置匹配）
buffer_size: 96000  # 每个并行环境的缓冲区大小
n_epochs: 5  # 每批数据的训练次数
n_minibatch: 4  # 小批量数量
learning_rate: 3.0e-4  # 学习率 
lr_a: 3.0e-4  # Actor学习率 
lr_c: 3.0e-4 # Critic学习率 
weight_decay: 1.0e-5  # 权重衰减 (增加适当的权重衰减)
lr_clip_range: [2.0e-6, 2.0e-6]  # 学习率裁剪范围

# PPO参数
vf_coef: 0.5  # 价值函数系数
ent_coef: 0.01  # 熵系数
target_kl: 0.01  # KL散度目标
clip_range: 0.2  # PPO裁剪范围 (稍微降低)
gamma: 0.99  # 折扣因子

# 技巧选项
use_linear_lr_decay: True  # 是否使用线性学习率衰减
end_factor_lr_decay: 0.5  # 学习率衰减结束因子
use_global_state: true  # 是否使用全局状态计算价值
use_value_clip: true  # 是否限制价值范围
value_clip_range: 0.2  # 价值裁剪范围
use_value_norm: true  # 是否使用奖励归一化
use_huber_loss: true  # 是否使用huber损失
huber_delta: 10.0  # huber损失delta参数
use_advnorm: true  # 是否使用优势归一化
use_gae: true  # 是否使用GAE计算累积奖励
gae_lambda: 0.95  # GAE lambda参数
use_grad_clip: true  # 是否使用梯度裁剪
grad_clip_norm: 5.0  # 梯度裁剪范数 (从10.0降低到5.0，更强的裁剪)

# 继续训练设置
continue_training: False  # 是否从已有的模型继续训练，设为False从头开始训练
load_model_dir_path: ""  # 要加载的模型的【目录】路径。如果为空，且continue_training为True，则自动查找最新模型目录。
model_filename_to_load: "best_model.pth" # 在指定的或自动查找到的目录中，要加载的模型文件名。
# 日志和保存
use_wandb: true  # 是否使用wandb记录
log_dir: "./logs/mappo/ce/"  # 日志目录
model_dir: "./models/mappo/ce/"  # 模型保存目录 